'use client';

import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';

import { <PERSON><PERSON>Header } from './header';
import { <PERSON><PERSON>Footer } from './footer';

// ----------------------------------------------------------------------

export function LomaLayout({ children, headerProps, footerProps }) {
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.default',
      }}
    >
      <LomaHeader {...headerProps} />
      
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          pt: { xs: 8, md: 10 }, // Account for fixed header
        }}
      >
        {children}
      </Box>
      
      <LomaFooter {...footerProps} />
    </Box>
  );
}

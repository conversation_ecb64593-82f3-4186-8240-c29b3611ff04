'use client';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';

import { RouterLink } from 'src/routes/components';
import { usePathname } from 'src/routes/hooks';

import { varAlpha } from 'minimal-shared/utils';

// ----------------------------------------------------------------------

export function LomaNavDesktop({ data, sx, ...other }) {
  const theme = useTheme();
  const pathname = usePathname();

  return (
    <Stack
      component="nav"
      direction="row"
      spacing={4}
      sx={[
        {
          height: 1,
          alignItems: 'center',
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      {data.map((item) => {
        const isActive = pathname === item.path;

        return (
          <Link
            key={item.title}
            component={RouterLink}
            href={item.path}
            underline="none"
            sx={{
              py: 1,
              px: 2,
              fontSize: 14,
              fontWeight: 500,
              borderRadius: 1,
              color: 'text.primary',
              transition: theme.transitions.create(['color', 'background-color'], {
                duration: theme.transitions.duration.shorter,
              }),
              '&:hover': {
                color: 'primary.main',
                bgcolor: varAlpha(theme.vars.palette.primary.mainChannel, 0.08),
              },
              ...(isActive && {
                color: 'primary.main',
                bgcolor: varAlpha(theme.vars.palette.primary.mainChannel, 0.12),
              }),
            }}
          >
            {item.title}
          </Link>
        );
      })}
    </Stack>
  );
}

'use client';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Drawer from '@mui/material/Drawer';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';
import { usePathname } from 'src/routes/hooks';

import { Iconify } from 'src/components/iconify';
import { Logo } from 'src/components/logo';
import { Scrollbar } from 'src/components/scrollbar';

// ----------------------------------------------------------------------

export function LomaNavMobile({ data, open, onClose, ...other }) {
  const pathname = usePathname();

  const renderHeader = () => (
    <Box
      sx={{
        py: 2,
        px: 2.5,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <Logo isSingle={false} />
      <IconButton onClick={onClose}>
        <Iconify icon="mingcute:close-line" />
      </IconButton>
    </Box>
  );

  const renderNavItems = () => (
    <Stack component="nav" spacing={0.5} sx={{ px: 2 }}>
      {data.map((item) => {
        const isActive = pathname === item.path;

        return (
          <Link
            key={item.title}
            component={RouterLink}
            href={item.path}
            onClick={onClose}
            underline="none"
            sx={{
              py: 1.5,
              px: 2,
              borderRadius: 1,
              typography: 'body2',
              fontWeight: 500,
              color: 'text.primary',
              transition: (theme) =>
                theme.transitions.create(['color', 'background-color'], {
                  duration: theme.transitions.duration.shorter,
                }),
              '&:hover': {
                color: 'primary.main',
                bgcolor: 'action.hover',
              },
              ...(isActive && {
                color: 'primary.main',
                bgcolor: 'action.selected',
              }),
            }}
          >
            {item.title}
          </Link>
        );
      })}
    </Stack>
  );

  const renderCTAButtons = () => (
    <Stack spacing={2} sx={{ px: 2.5, pb: 3 }}>
      <Button
        component={RouterLink}
        href={paths.quote}
        variant="outlined"
        color="primary"
        fullWidth
        onClick={onClose}
      >
        Báo giá ngay
      </Button>

      <Button
        component={RouterLink}
        href={paths.mockup}
        variant="contained"
        color="primary"
        fullWidth
        startIcon={<Iconify icon="solar:palette-2-bold" />}
        onClick={onClose}
      >
        Tạo mockup
      </Button>
    </Stack>
  );

  return (
    <Drawer
      open={open}
      onClose={onClose}
      anchor="right"
      slotProps={{
        backdrop: { invisible: true },
      }}
      PaperProps={{
        sx: { width: 280 },
      }}
      {...other}
    >
      {renderHeader()}

      <Divider />

      <Scrollbar fillContent>
        <Box sx={{ py: 3 }}>
          {renderNavItems()}
        </Box>
      </Scrollbar>

      <Divider />

      {renderCTAButtons()}
    </Drawer>
  );
}

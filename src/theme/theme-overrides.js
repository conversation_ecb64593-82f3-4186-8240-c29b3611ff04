'use client';

import { createPaletteChannel } from 'minimal-shared/utils';

// ----------------------------------------------------------------------

// Loma Brand Colors - Industrial & Professional
export const themeOverrides = {
  colorSchemes: {
    light: {
      palette: {
        // Primary: Industrial Blue - Trust & Professionalism
        primary: createPaletteChannel({
          lighter: '#EBF4FF',
          light: '#7DD3FC',
          main: '#2563EB',
          dark: '#1D4ED8',
          darker: '#1E3A8A',
          contrastText: '#FFFFFF',
        }),
        // Secondary: Warm Orange - Energy & Creativity
        secondary: createPaletteChannel({
          lighter: '#FFF7ED',
          light: '#FDBA74',
          main: '#F97316',
          dark: '#EA580C',
          darker: '#C2410C',
          contrastText: '#FFFFFF',
        }),
        // Success: Fresh Green - Quality & Success
        success: createPaletteChannel({
          lighter: '#F0FDF4',
          light: '#86EFAC',
          main: '#10B981',
          dark: '#059669',
          darker: '#047857',
          contrastText: '#FFFFFF',
        }),
        // Warning: Amber - Attention & Process
        warning: createPaletteChannel({
          lighter: '#FFFBEB',
          light: '#FCD34D',
          main: '#F59E0B',
          dark: '#D97706',
          darker: '#92400E',
          contrastText: '#FFFFFF',
        }),
        // Error: Red - Issues & Alerts
        error: createPaletteChannel({
          lighter: '#FEF2F2',
          light: '#FCA5A5',
          main: '#EF4444',
          dark: '#DC2626',
          darker: '#991B1B',
          contrastText: '#FFFFFF',
        }),
      },
    },
    dark: {
      palette: {
        // Primary: Industrial Blue - Trust & Professionalism
        primary: createPaletteChannel({
          lighter: '#EBF4FF',
          light: '#7DD3FC',
          main: '#3B82F6',
          dark: '#2563EB',
          darker: '#1D4ED8',
          contrastText: '#FFFFFF',
        }),
        // Secondary: Warm Orange - Energy & Creativity
        secondary: createPaletteChannel({
          lighter: '#FFF7ED',
          light: '#FDBA74',
          main: '#FB923C',
          dark: '#F97316',
          darker: '#EA580C',
          contrastText: '#FFFFFF',
        }),
        // Success: Fresh Green - Quality & Success
        success: createPaletteChannel({
          lighter: '#F0FDF4',
          light: '#86EFAC',
          main: '#22C55E',
          dark: '#10B981',
          darker: '#059669',
          contrastText: '#FFFFFF',
        }),
        // Warning: Amber - Attention & Process
        warning: createPaletteChannel({
          lighter: '#FFFBEB',
          light: '#FCD34D',
          main: '#FBBF24',
          dark: '#F59E0B',
          darker: '#D97706',
          contrastText: '#FFFFFF',
        }),
        // Error: Red - Issues & Alerts
        error: createPaletteChannel({
          lighter: '#FEF2F2',
          light: '#FCA5A5',
          main: '#F87171',
          dark: '#EF4444',
          darker: '#DC2626',
          contrastText: '#FFFFFF',
        }),
      },
    },
  },
};

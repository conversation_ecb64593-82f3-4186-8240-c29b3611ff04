'use client';

import { useId } from 'react';
import { mergeClasses } from 'minimal-shared/utils';

import Link from '@mui/material/Link';
import { styled, useTheme } from '@mui/material/styles';

import { RouterLink } from 'src/routes/components';

import { logoClasses } from './classes';

// ----------------------------------------------------------------------

export function Logo({ sx, disabled, className, href = '/', isSingle = true, ...other }) {
  const theme = useTheme();

  const gradientId = useId();

  const TEXT_PRIMARY = theme.vars.palette.text.primary;
  const PRIMARY_LIGHT = theme.vars.palette.primary.light;
  const PRIMARY_MAIN = theme.vars.palette.primary.main;
  const PRIMARY_DARKER = theme.vars.palette.primary.dark;

  /*
    * OR using local (public folder)
    *
    const singleLogo = (
      <img
        alt="Single logo"
        src={`${CONFIG.assetsDir}/logo/logo-single.svg`}
        width="100%"
        height="100%"
      />
    );

    const fullLogo = (
      <img
        alt="Full logo"
        src={`${CONFIG.assetsDir}/logo/logo-full.svg`}
        width="100%"
        height="100%"
      />
    );
    *
    */

  const singleLogo = (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient
          id={`${gradientId}-single`}
          x1="0"
          y1="0"
          x2="40"
          y2="40"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={PRIMARY_MAIN} />
          <stop offset="1" stopColor={PRIMARY_DARKER} />
        </linearGradient>
      </defs>
      {/* Bag shape */}
      <path
        fill={`url(#${gradientId}-single)`}
        d="M8 12h24c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H8c-1.1 0-2-.9-2-2V14c0-1.1.9-2 2-2z"
      />
      {/* Bag handles */}
      <path
        stroke={PRIMARY_MAIN}
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
        d="M14 12V8c0-2.2 1.8-4 4-4h4c2.2 0 4 1.8 4 4v4"
      />
      {/* Letter L */}
      <text
        x="20"
        y="26"
        textAnchor="middle"
        fill="white"
        fontSize="14"
        fontWeight="bold"
        fontFamily="system-ui, -apple-system, sans-serif"
      >
        L
      </text>
    </svg>
  );

  const fullLogo = (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 120 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient
          id={`${gradientId}-full`}
          x1="0"
          y1="0"
          x2="36"
          y2="36"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={PRIMARY_MAIN} />
          <stop offset="1" stopColor={PRIMARY_DARKER} />
        </linearGradient>
      </defs>
      {/* Bag icon */}
      <g>
        {/* Bag shape */}
        <path
          fill={`url(#${gradientId}-full)`}
          d="M6 10h24c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V12c0-1.1.9-2 2-2z"
        />
        {/* Bag handles */}
        <path
          stroke={PRIMARY_MAIN}
          strokeWidth="1.5"
          strokeLinecap="round"
          fill="none"
          d="M12 10V7c0-1.7 1.3-3 3-3h6c1.7 0 3 1.3 3 3v3"
        />
      </g>

      {/* LOMA text */}
      <text
        x="44"
        y="25"
        fill={TEXT_PRIMARY}
        fontSize="20"
        fontWeight="bold"
        fontFamily="system-ui, -apple-system, sans-serif"
      >
        LOMA
      </text>
    </svg>
  );

  return (
    <LogoRoot
      component={RouterLink}
      href={href}
      aria-label="Logo"
      underline="none"
      className={mergeClasses([logoClasses.root, className])}
      sx={[
        {
          width: 40,
          height: 40,
          ...(!isSingle && { width: 102, height: 36 }),
          ...(disabled && { pointerEvents: 'none' }),
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      {isSingle ? singleLogo : fullLogo}
    </LogoRoot>
  );
}

// ----------------------------------------------------------------------

const LogoRoot = styled(Link)(() => ({
  flexShrink: 0,
  color: 'transparent',
  display: 'inline-flex',
  verticalAlign: 'middle',
}));
